using TeamFlow.Identity.Core.Enums.Users;
using UserRole = TeamFlow.Shared.Contracts.Enums.UserRole;

namespace TeamFlow.Identity.Application.Contracts.Dto.ViewModels.Profile;

public record PublicUserProfileViewModel
{
    public required Guid Id { get; init; }
    public required string FirstName { get; init; }
    public required string LastName { get; init; }
    public required string Login { get; init; } = string.Empty;
    public required UserOnlineStatus OnlineStatus { get; init; }
    public required UserRole Role { get; init; }
    public required Guid PositionId { get; init; }
    public required string PositionName { get; init; } = string.Empty;
    public string? AvatarUrl { get; init; } = null;
    public DateTime CreatedAt { get; init; }
    public List<UserSkillViewModel>? Skills { get; init; } = [];
}