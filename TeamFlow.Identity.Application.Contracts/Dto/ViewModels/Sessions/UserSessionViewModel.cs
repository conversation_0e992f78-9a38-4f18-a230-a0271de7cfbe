using TeamFlow.Identity.Core.Enums;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels.AuthEvents;

namespace TeamFlow.Identity.Application.Contracts.Dto.ViewModels.Sessions;

public record UserSessionViewModel
{
    public Guid Id { get; init; }
    public Guid UserId { get; init; }
    public DateTime ExpiresAt { get; init; }
    public bool IsActive { get; init; }
    public DateTime? LastActivity { get; init; }
    public DateTime? TerminatedAt { get; init; }
    public SessionTerminationReason? TerminationReason { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; init; }
    public List<AuthEventViewModel> AuthEvents { get; init; } = [];
}