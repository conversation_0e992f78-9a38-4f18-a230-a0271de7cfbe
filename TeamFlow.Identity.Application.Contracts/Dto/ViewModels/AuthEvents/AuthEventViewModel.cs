using TeamFlow.Identity.Core.Enums;
using TeamFlow.Identity.Core.Enums.Jwt;

namespace TeamFlow.Identity.Application.Contracts.Dto.ViewModels.AuthEvents;

public record AuthEventViewModel
{
    public Guid Id { get; init; }
    public AuthEventType EventType { get; init; }
    public string? IpAddress { get; init; }
    public string? Country { get; init; }
    public string? City { get; init; }
    public DeviceKind DeviceType { get; init; }
    public string? DeviceName { get; init; }
    public string? OsName { get; init; }
    public string? OsVersion { get; init; }
    public string? BrowserName { get; init; }
    public string? BrowserVersion { get; init; }
    public bool IsSuccessful { get; init; }
    public string? FailureReason { get; init; }
    public DateTime CreatedAt { get; init; }
}