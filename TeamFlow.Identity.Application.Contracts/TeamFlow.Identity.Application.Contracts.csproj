<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="Dto\ViewModels\Users\" />
      <Folder Include="Features\" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\src\Identity\TeamFlow.Identity.Core\TeamFlow.Identity.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="MediatR.Contracts" Version="2.0.1" />
    </ItemGroup>

</Project>
