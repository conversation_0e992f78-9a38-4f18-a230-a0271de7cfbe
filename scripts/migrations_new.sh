#!/bin/bash

# TeamFlow Migration Management Script
# This script provides a convenient way to manage Entity Framework migrations
# across different projects in the TeamFlow solution.

# Mapping of context names to project paths and startup projects
# Note: Only projects that use Entity Framework and have DbContext should be listed here
declare -A MIGRATION_PROJECTS=(
    ["Identity"]="src/Identity/TeamFlow.Identity.Persistence"
)

declare -A STARTUP_PROJECTS=(
    ["Identity"]="src/Identity/TeamFlow.Identity.Api"
)

# Projects that don't support EF migrations
declare -A UNSUPPORTED_PROJECTS=(
    ["Tasks"]="Uses MongoDB - no Entity Framework migrations needed"
    ["Gateway"]="API Gateway - no database migrations needed"
)

# Function to display usage
usage() {
    echo "TeamFlow Migration Management Script"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  add <migration_name>  - Add a new migration"
    echo "  update               - Apply migrations to database"
    echo "  remove               - Remove the last migration"
    echo ""
    echo "Options:"
    echo "  -c, --context        - Specify context (currently only Identity is supported)"
    echo "  -p, --project        - Specify project path directly"
    echo ""
    echo "Examples:"
    echo "  $0 add AddUserTable --context Identity"
    echo "  $0 update --context Identity"
    echo "  $0 remove --context Identity"
    echo "  $0 add AddUserTable --project \"src/Identity/TeamFlow.Identity.Persistence\""
    echo ""
    echo "Available contexts:"
    for key in "${!MIGRATION_PROJECTS[@]}"; do
        echo "  $key -> ${MIGRATION_PROJECTS[$key]}"
    done
    echo ""
    echo "Unsupported contexts (no Entity Framework migrations):"
    for key in "${!UNSUPPORTED_PROJECTS[@]}"; do
        echo "  $key -> ${UNSUPPORTED_PROJECTS[$key]}"
    done
    exit 1
}

# Function to get project info (project path and startup project)
get_project_info() {
    local context=$1
    local project=$2

    if [ ! -z "$project" ]; then
        echo "$project" ""
        return
    fi

    if [ ! -z "$context" ] && [ ! -z "${MIGRATION_PROJECTS[$context]}" ]; then
        local project_path="${MIGRATION_PROJECTS[$context]}"
        local startup_project="${STARTUP_PROJECTS[$context]}"
        echo "$project_path" "$startup_project"
        return
    fi

    if [ ! -z "$context" ] && [ ! -z "${UNSUPPORTED_PROJECTS[$context]}" ]; then
        echo "Error: Context '$context' does not support Entity Framework migrations." >&2
        echo "Reason: ${UNSUPPORTED_PROJECTS[$context]}" >&2
        exit 1
    fi

    if [ ! -z "$context" ]; then
        echo "Error: Context '$context' not found." >&2
        echo "Available contexts: ${!MIGRATION_PROJECTS[*]}" >&2
        echo "Unsupported contexts:" >&2
        for key in "${!UNSUPPORTED_PROJECTS[@]}"; do
            echo "  $key: ${UNSUPPORTED_PROJECTS[$key]}" >&2
        done
        exit 1
    fi

    echo "Error: Either --context or --project must be specified" >&2
    usage
}

# Function to run dotnet ef command with optional startup project
run_dotnet_ef_command() {
    local cmd=("$@")
    local startup_project="$1"
    shift
    local ef_cmd=("$@")

    if [ ! -z "$startup_project" ]; then
        ef_cmd+=("--startup-project" "$startup_project")
    fi

    echo "Running: ${ef_cmd[*]}"
    "${ef_cmd[@]}"
}

# Parse command line arguments
COMMAND=""
NAME=""
CONTEXT=""
PROJECT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        add)
            COMMAND="add"
            if [[ -z $2 ]]; then
                echo "Error: Migration name is required for 'add' command"
                usage
            fi
            NAME="$2"
            shift 2
            ;;
        update)
            COMMAND="update"
            shift
            ;;
        remove)
            COMMAND="remove"
            shift
            ;;
        -c|--context)
            CONTEXT="$2"
            shift 2
            ;;
        -p|--project)
            PROJECT="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Check if command was provided
if [ -z "$COMMAND" ]; then
    echo "Error: No command specified"
    usage
fi

# Validate context and project before proceeding
if [ ! -z "$CONTEXT" ] && [ ! -z "${UNSUPPORTED_PROJECTS[$CONTEXT]}" ]; then
    echo "Error: Context '$CONTEXT' does not support Entity Framework migrations." >&2
    echo "Reason: ${UNSUPPORTED_PROJECTS[$CONTEXT]}" >&2
    exit 1
fi

# Get project info
PROJECT_INFO=$(get_project_info "$CONTEXT" "$PROJECT")
read -r PROJECT_PATH STARTUP_PROJECT <<< "$PROJECT_INFO"

# Execute command
case $COMMAND in
    add)
        echo "Adding new migration: $NAME to project: $PROJECT_PATH"
        if [ ! -z "$STARTUP_PROJECT" ]; then
            echo "Using startup project: $STARTUP_PROJECT"
        fi
        
        cmd=("dotnet" "ef" "migrations" "add" "$NAME" "--project" "$PROJECT_PATH")
        if [ ! -z "$STARTUP_PROJECT" ]; then
            cmd+=("--startup-project" "$STARTUP_PROJECT")
        fi
        
        echo "Running: ${cmd[*]}"
        "${cmd[@]}"
        ;;
    update)
        echo "Applying migrations to database for project: $PROJECT_PATH"
        if [ ! -z "$STARTUP_PROJECT" ]; then
            echo "Using startup project: $STARTUP_PROJECT"
        fi
        
        cmd=("dotnet" "ef" "database" "update" "--project" "$PROJECT_PATH")
        if [ ! -z "$STARTUP_PROJECT" ]; then
            cmd+=("--startup-project" "$STARTUP_PROJECT")
        fi
        
        echo "Running: ${cmd[*]}"
        "${cmd[@]}"
        ;;
    remove)
        echo "Removing last migration from project: $PROJECT_PATH"
        if [ ! -z "$STARTUP_PROJECT" ]; then
            echo "Using startup project: $STARTUP_PROJECT"
        fi
        
        cmd=("dotnet" "ef" "migrations" "remove" "--project" "$PROJECT_PATH")
        if [ ! -z "$STARTUP_PROJECT" ]; then
            cmd+=("--startup-project" "$STARTUP_PROJECT")
        fi
        
        echo "Running: ${cmd[*]}"
        "${cmd[@]}"
        ;;
    *)
        usage
        ;;
esac
