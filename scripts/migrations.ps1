<#
.SYNOPSIS
TeamFlow Migration Management Script

.DESCRIPTION
This script provides a convenient way to manage Entity Framework migrations
across different projects in the TeamFlow solution.

.PARAMETER Command
The command to execute: add, update, or remove

.PARAMETER Name
Name of the migration (required for 'add' command)

.PARAMETER Context
Context name (currently only Identity is supported)

.PARAMETER Project
Direct project path

.EXAMPLE
.\scripts\migrations.ps1 -Command add -Name "AddUserTable" -Context Identity

.EXAMPLE
.\scripts\migrations.ps1 -Command update -Context Identity

.EXAMPLE
.\scripts\migrations.ps1 -Command remove -Context Identity

.EXAMPLE
.\scripts\migrations.ps1 -Command add -Name "AddUserTable" -Project "src/Identity/TeamFlow.Identity.Persistence"
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("add", "update", "remove")]
    [string]$Command,

    [Parameter(Mandatory=$false)]
    [string]$Name,

    [Parameter(Mandatory=$false)]
    [string]$Context,

    [Parameter(Mandatory=$false)]
    [string]$Project,

    [Parameter(Mandatory=$false)]
    [switch]$Help
)

$ErrorActionPreference = "Stop"

# Mapping of context names to project paths and startup projects
# Note: Only projects that use Entity Framework and have DbContext should be listed here
$MigrationProjects = @{
    "Identity" = @{
        "Project" = "src/Identity/TeamFlow.Identity.Persistence"
        "Startup" = "src/Identity/TeamFlow.Identity.Api"
    }
}

# Projects that don't support EF migrations
$UnsupportedProjects = @{
    "Tasks" = "Uses MongoDB - no Entity Framework migrations needed"
    "Gateway" = "API Gateway - no database migrations needed"
}

function Show-Usage {
    Write-Host "TeamFlow Migration Management Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\scripts\migrations.ps1 -Command <command> [options]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Commands:" -ForegroundColor Cyan
    Write-Host "  add                  - Add a new migration"
    Write-Host "  update               - Apply migrations to database"
    Write-Host "  remove               - Remove the last migration"
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  -Name <string>       - Migration name (required for 'add' command)"
    Write-Host "  -Context <string>    - Context name (currently only Identity is supported)"
    Write-Host "  -Project <string>    - Direct project path"
    Write-Host "  -Help                - Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Cyan
    Write-Host "  .\scripts\migrations.ps1 -Command add -Name `"AddUserTable`" -Context Identity"
    Write-Host "  .\scripts\migrations.ps1 -Command update -Context Identity"
    Write-Host "  .\scripts\migrations.ps1 -Command remove -Context Identity"
    Write-Host "  .\scripts\migrations.ps1 -Command add -Name `"AddUserTable`" -Project `"src/Identity/TeamFlow.Identity.Persistence`""
    Write-Host ""
    Write-Host "Available contexts:" -ForegroundColor Cyan
    foreach ($key in $MigrationProjects.Keys) {
        Write-Host "  $key -> $($MigrationProjects[$key].Project)"
    }
    Write-Host ""
    Write-Host "Unsupported contexts (no Entity Framework migrations):" -ForegroundColor Cyan
    foreach ($key in $UnsupportedProjects.Keys) {
        Write-Host "  $key -> $($UnsupportedProjects[$key])"
    }
    exit 0
}

function Get-ProjectInfo {
    param(
        [string]$Context,
        [string]$Project
    )

    if ($Project) {
        return @{
            "ProjectPath" = $Project
            "StartupProject" = $null
        }
    }

    if ($Context -and $MigrationProjects.ContainsKey($Context)) {
        return @{
            "ProjectPath" = $MigrationProjects[$Context].Project
            "StartupProject" = $MigrationProjects[$Context].Startup
        }
    }

    if ($Context -and $UnsupportedProjects.ContainsKey($Context)) {
        Write-Error "Context '$Context' does not support Entity Framework migrations. Reason: $($UnsupportedProjects[$Context])"
        exit 1
    }

    if ($Context) {
        $availableContexts = $MigrationProjects.Keys -join ', '
        Write-Error "Context '$Context' not found.`nAvailable contexts: $availableContexts`nUnsupported contexts:"
        foreach ($key in $UnsupportedProjects.Keys) {
            Write-Error "  $key`: $($UnsupportedProjects[$key])"
        }
        exit 1
    }

    Write-Error "Either -Context or -Project must be specified"
    exit 1
}

function Invoke-DotnetEfCommand {
    param(
        [string[]]$Arguments,
        [string]$StartupProject
    )

    if ($StartupProject) {
        $Arguments += "--startup-project", $StartupProject
    }

    $commandString = "dotnet ef " + ($Arguments -join " ")
    Write-Host "Running: $commandString" -ForegroundColor Yellow

    try {
        & dotnet ef @Arguments
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Command failed with exit code $LASTEXITCODE"
            exit $LASTEXITCODE
        }
    }
    catch {
        Write-Error "Error executing command: $_"
        exit 1
    }
}

function Add-Migration {
    param(
        [string]$MigrationName,
        [string]$ProjectPath,
        [string]$StartupProject
    )

    Write-Host "Adding new migration: $MigrationName to project: $ProjectPath" -ForegroundColor Green
    if ($StartupProject) {
        Write-Host "Using startup project: $StartupProject" -ForegroundColor Green
    }

    $arguments = @("migrations", "add", $MigrationName, "--project", $ProjectPath)
    Invoke-DotnetEfCommand -Arguments $arguments -StartupProject $StartupProject
}

function Update-Database {
    param(
        [string]$ProjectPath,
        [string]$StartupProject
    )

    Write-Host "Applying migrations to database for project: $ProjectPath" -ForegroundColor Green
    if ($StartupProject) {
        Write-Host "Using startup project: $StartupProject" -ForegroundColor Green
    }

    $arguments = @("database", "update", "--project", $ProjectPath)
    Invoke-DotnetEfCommand -Arguments $arguments -StartupProject $StartupProject
}

function Remove-Migration {
    param(
        [string]$ProjectPath,
        [string]$StartupProject
    )

    Write-Host "Removing last migration from project: $ProjectPath" -ForegroundColor Green
    if ($StartupProject) {
        Write-Host "Using startup project: $StartupProject" -ForegroundColor Green
    }

    $arguments = @("migrations", "remove", "--project", $ProjectPath)
    Invoke-DotnetEfCommand -Arguments $arguments -StartupProject $StartupProject
}

# Show help if requested or no command provided
if ($Help -or [string]::IsNullOrEmpty($Command)) {
    Show-Usage
}

# Get project information
try {
    $projectInfo = Get-ProjectInfo -Context $Context -Project $Project
    $ProjectPath = $projectInfo.ProjectPath
    $StartupProject = $projectInfo.StartupProject
}
catch {
    Write-Error $_.Exception.Message
    exit 1
}

# Execute command
switch ($Command) {
    "add" {
        if ([string]::IsNullOrEmpty($Name)) {
            Write-Error "Migration name is required for 'add' command"
            exit 1
        }
        Add-Migration -MigrationName $Name -ProjectPath $ProjectPath -StartupProject $StartupProject
    }
    "update" {
        Update-Database -ProjectPath $ProjectPath -StartupProject $StartupProject
    }
    "remove" {
        Remove-Migration -ProjectPath $ProjectPath -StartupProject $StartupProject
    }
    default {
        Write-Error "Unknown command: $Command"
        Show-Usage
    }
}