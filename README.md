# TeamFlow.Task

## Описание проекта

TeamFlow.Task - это микросервис для управления задачами и проектами в рамках командной работы. Система позволяет
создавать проекты, формировать команды, назначать задачи и отслеживать их выполнение.

## Архитектура

Проект следует принципам Clean Architecture и состоит из следующих основных компонентов:

- TeamFlow.Task.Core : Содержит бизнес-модели, интерфейсы и абстракции
- TeamFlow.Task.Application : Содержит бизнес-логику и сервисы
- TeamFlow.Task.Infrastructure : Содержит реализации инфраструктурных сервисов (БД, внешние API)
- TeamFlow.Task.Api : Содержит API-контроллеры и конфигурацию приложения

### Зависимости между проектами

- Core не должен зависеть от других проектов
- Application может зависеть только от Core
- Infrastructure может зависеть от Core и Application
- Api может зависеть от всех проектов

## Основные возможности

- Управление проектами (создание, редактирование, удаление)
- Управление командами (создание, назначение участников, назначение тимлида)
- Управление задачами (создание, назначение исполнителей, отслеживание статуса)
- Канбан-доска для визуализации рабочего процесса
- Приоритизация задач и проектов
- Отслеживание времени выполнения задач

## Технологии

- .NET 9.0
- ASP.NET Core
- Entity Framework Core
- MongoDB
- gRPC для межсервисного взаимодействия
- FluentValidation для валидации данных
- FluentResults для обработки результатов операций
- Serilog для логирования
- Docker для контейнеризации

## Запуск проекта

### Требования

- .NET 9.0 SDK
- Runtime (aspnetcore-runtime)
- Docker
- Docker Compose

### Установка требуемых пакетов

Если вы **линуксоид**, то вы собственно без проблем подменить apt на ваш пакетный менеджер, и найдете какие репозитории
нужно вам добавить.

Поэтому оставляю примеры только для дистрибутивов использующих apt

```bash
sudo apt-get update && \
sudo apt-get install -y dotnet-sdk-9.0
```

```bash
sudo apt-get update && \
sudo apt-get install -y aspnetcore-runtime-9.0
```

Если вы windows enjoyer, то качайте **[.NET](https://dotnet.microsoft.com/en-us/download)** с офф сайта или через winget

MacOS братья, качайте тоже с **[офф сайта](https://dotnet.microsoft.com/en-us/download)** или через brew

### Локальный запуск

1. Клонировать репозиторий:

    ```bash
    git clone https://github.com/your-repo/TeamFlow.Task.git
    cd TeamFlow.Task 
    ```
2. Настроить переменные окружения (скопировать [.env.example](.env.example) в .env и отредактировать при необходимости)
3. Запустить с помощью Docker Compose:
    ```bash
   docker-compose up -d
    ```
4. [Применить миграции](#применение-миграций)
5. Или запустить локально:
    ```bash
    dotnet restore
    dotnet build
    cd src/TeamFlow.Task.Api
    dotnet run
    ```

## Стиль кода

Проект следует конвенции стиля кода, описанной в [документации](./docs/code-style-convention.md).

## Применение миграций {#применение-миграций}

TeamFlow использует Entity Framework Core для управления миграциями базы данных в домене Identity. Для удобства работы с миграциями предоставлены скрипты на разных платформах.

### Поддерживаемые контексты

- ✅ **Identity** - Entity Framework Core с PostgreSQL (поддерживает миграции)
- ❌ **Tasks** - MongoDB (миграции не нужны)
- ❌ **Gateway** - API Gateway (нет базы данных)

### Быстрый старт

```bash
# Python
python scripts/migrations.py add AddUserTable --context Identity

# PowerShell
.\scripts\migrations.ps1 -Command add -Name "AddUserTable" -Context Identity

# Bash
./scripts/migrations_new.sh add AddUserTable --context Identity
```

### Прямые команды dotnet ef

```bash
# Добавить миграцию
dotnet ef migrations add AddUserTable --project src/Identity/TeamFlow.Identity.Persistence --startup-project src/Identity/TeamFlow.Identity.Api

# Применить миграции
dotnet ef database update --project src/Identity/TeamFlow.Identity.Persistence --startup-project src/Identity/TeamFlow.Identity.Api
```

📖 **Подробное руководство**: [Управление миграциями](./docs/migrations-guide.md)

## Доступные руководства

- [Управление миграциями](./docs/migrations-guide.md)
- [Асинхронное программирование](./docs/async-programming-guide.md)
- [Внутрення библиотека `ExpressionBuilder`](./docs/expression-builder-guide.md)
- [Руководство по стилю кода](./docs/code-style-convention.md)
