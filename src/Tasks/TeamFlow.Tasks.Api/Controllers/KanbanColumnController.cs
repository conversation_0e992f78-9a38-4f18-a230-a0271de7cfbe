using FluentResults.Extensions.AspNetCore;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Api.Api.Filtering.KanbanColumns;
using TeamFlow.Tasks.Api.Api.Requests.KanbanColumns;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Commands;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.KanbanColumn;

namespace TeamFlow.Tasks.Api.Controllers;

// [Authorize]
[Route("api/[controller]")]
[ApiController]
public class KanbanColumnController : ControllerBase
{
    private readonly ISender _sender;

    public KanbanColumnController(ISender sender)
    {
        _sender = sender;
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetKanbanColumnByIdQuery(id), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet("name/{name}")]
    public async Task<IActionResult> GetByName(string name, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetKanbanColumnByNameQuery(name), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet]
    public async Task<IActionResult> GetManyAsync(
        [FromQuery] KanbanColumnFilteringQueryParameters filterParameters,
        [FromQuery] KanbanColumnSortingQueryParameters sortingParameters,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var pageable = new Pageable(pageNumber, pageSize);
        var filterCriteria = new KanbanColumnFilterCriteria(filterParameters.Name, filterParameters.ProjectId,
            filterParameters.Order, filterParameters.MinOrder, filterParameters.MaxOrder, searchTerm);
        var sortingCriteria = new KanbanColumnSortingCriteria(sortingParameters.SortField, sortingParameters.Direction);

        var columns =
            await _sender.Send(new GetKanbanColumnsQuery(pageable, filterCriteria, sortingCriteria), cancellationToken);

        return columns.ToActionResult();
    }


    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateKanbanColumnCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(request, cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateKanbanColumnRequest request,
        CancellationToken cancellationToken = default)
    {
        var result =
            await _sender.Send(new UpdateKanbanColumnCommand(id, request.ProjectId, request.Name, request.Status, request.Order),
                cancellationToken);
        return result.ToActionResult();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new DeleteKanbanColumnCommand(id), cancellationToken);
        return result.ToActionResult();
    }
}