using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Commands;

namespace TeamFlow.Tasks.Application.Validators.KanbanColumn;

public class CreateKanbanColumnCommandValidator : AbstractValidator<CreateKanbanColumnCommand>
{
    public CreateKanbanColumnCommandValidator()
    {
        RuleFor(x => x.ProjectId)
            .NotEmpty();

        RuleFor(x => x.Name)
            .NotEmpty()
            .Length(1, 50);

        RuleFor(x => x.Status)
            .IsInEnum()
            .WithMessage("Статус колонки должен быть корректным значением");

        RuleFor(x => x.Order)
            .GreaterThanOrEqualTo(0);
    }
}