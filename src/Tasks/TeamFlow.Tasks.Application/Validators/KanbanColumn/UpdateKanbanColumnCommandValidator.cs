using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Commands;

namespace TeamFlow.Tasks.Application.Validators.KanbanColumn;

public class UpdateKanbanColumnCommandValidator : AbstractValidator<UpdateKanbanColumnCommand>
{
    public UpdateKanbanColumnCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();

        RuleFor(x => x.ProjectId)
            .NotEmpty()
            .When(x => x.ProjectId.HasValue);

        RuleFor(x => x.Name)
            .Length(1, 50)
            .When(x => x.Name != null);

        RuleFor(x => x.Status)
            .IsInEnum()
            .WithMessage("Статус колонки должен быть корректным значением")
            .When(x => x.Status.HasValue);

        RuleFor(x => x.Order)
            .GreaterThanOrEqualTo(0)
            .When(x => x.Order.HasValue);
    }
}