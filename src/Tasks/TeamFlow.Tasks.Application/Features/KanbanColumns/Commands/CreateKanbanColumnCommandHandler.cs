using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.KanbanColumn.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.KanbanColumns.Commands;

public sealed class CreateKanbanColumnCommandHandler : IRequestHandler<CreateKanbanColumnCommand, Result<Guid>>
{
    private readonly IRepository<KanbanColumn, Guid> _columnsRepository;
    private readonly ILogger<CreateKanbanColumnCommandHandler> _logger;
    private readonly IValidator<CreateKanbanColumnCommand> _validator;

    public CreateKanbanColumnCommandHandler(IRepository<KanbanColumn, Guid> columnsRepository, ILogger<CreateKanbanColumnCommandHandler> logger, IValidator<CreateKanbanColumnCommand> validator)
    {
        _columnsRepository = columnsRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<Guid>> Handle(CreateKanbanColumnCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Создание новой колонки с именем {Name} для проекта {ProjectId}",
            request.Name, request.ProjectId);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "создании колонки");
            return Result.Fail(ErrorsFactory.FromValidationResult("KanbanColumn", validationResult));
        }

        var isColumnExists = await _columnsRepository
            .FindAnyAsync(x => x.Name == request.Name, cancellationToken);

        if (isColumnExists)
        {
            _logger.LogWarning("Колонка с именем {Name} уже существует", request.Name);
            return Result.Fail(ErrorsFactory.AlreadyExists(nameof(KanbanColumn), "Name", request.Name));
        }

        var column = new KanbanColumn
        {
            Id = Guid.CreateVersion7(),
            Name = request.Name,
            ProjectId = request.ProjectId,
            Status = request.Status,
            Order = request.Order,
        };

        await _columnsRepository.AddAsync(column, cancellationToken);
        _logger.LogInformation("Колонка успешно создана с ID: {Id}", column.Id);
        return Result.Ok(column.Id);
    }
}