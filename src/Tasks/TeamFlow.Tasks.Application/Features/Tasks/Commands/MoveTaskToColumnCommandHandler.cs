using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Utils;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services;
using TeamFlow.Tasks.Core.Entities;
using Task = System.Threading.Tasks.Task;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class MoveTaskToColumnCommandHandler : IRequestHandler<MoveTaskToColumnCommand, Result>
{
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly IRepository<KanbanColumn, Guid> _columnsRepository;
    private readonly ITaskStatusTracker _taskStatusTracker;
    private readonly ILogger<MoveTaskToColumnCommandHandler> _logger;
    private readonly IValidator<MoveTaskToColumnCommand> _validator;
    private readonly ITaskOrderService _taskOrderService;

    public MoveTaskToColumnCommandHandler(IRepository<TaskEntity, Guid> tasksRepository,
        IRepository<KanbanColumn, Guid> columnsRepository, ITaskStatusTracker taskStatusTracker,
        ILogger<MoveTaskToColumnCommandHandler> logger, IValidator<MoveTaskToColumnCommand> validator,
        ITaskOrderService taskOrderService)
    {
        _tasksRepository = tasksRepository;
        _columnsRepository = columnsRepository;
        _taskStatusTracker = taskStatusTracker;
        _logger = logger;
        _validator = validator;
        _taskOrderService = taskOrderService;
    }

    public async Task<Result> Handle(MoveTaskToColumnCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Перемещение задачи {TaskId} в колонку {ColumnId} с порядком {NewOrder}", request.TaskId,
            request.ColumnId, request.NewOrder);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "перемещении задачи");
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        var taskEntityTask =
            _tasksRepository.GetOneAsync(x => x.Id == request.TaskId, cancellationToken: cancellationToken);
        var columnTask =
            _columnsRepository.GetOneAsync(x => x.Id == request.ColumnId, cancellationToken: cancellationToken);

        await Task.WhenAll(taskEntityTask, columnTask);

        var (task, column) = (taskEntityTask.Result, columnTask.Result);

        if (task is null)
        {
            _logger.LogWarning("Задача с идентификатором {TaskId} не найдена", request.TaskId);
            return Result.Fail(ErrorsFactory.NotFound("Task", $"Задача с ID '{request.TaskId}' не найдена"));
        }

        if (column is null)
        {
            _logger.LogWarning("Колонка с идентификатором {ColumnId} не найдена", request.ColumnId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(KanbanColumn),
                $"Колонка с идентификатором {request.ColumnId} не найдена"));
        }

        if (task.ProjectId != column.ProjectId)
        {
            _logger.LogWarning(
                "Невозможно переместить задачу {TaskId} в колонку {ColumnId} из другого проекта. TaskProjectId: {TaskProjectId}, ColumnProjectId: {ColumnProjectId}",
                request.TaskId, request.ColumnId, task.ProjectId, column.ProjectId);
            return Result.Fail(ErrorsFactory
                .BadRequest("Task", "Невозможно переместить задачу в колонку из другого проекта")
                .WithMetadata("TaskProjectId", task.ProjectId.ToString())
                .WithMetadata("ColumnProjectId", column.ProjectId.ToString()));
        }

        var newColumnId = ValueComparer.GetNewValueIfUpdated(task.KanbanColumnId, request.ColumnId);
        var newKanbanOrder = ValueComparer.GetNewValueIfUpdated(task.KanbanOrder, request.NewOrder);

        //TODO переделать в switch?
        if (newColumnId is null && newKanbanOrder is null)
        {
            _logger.LogWarning("Не указаны изменения для задачи {TaskId}. Необходимо изменить колонку или порядок",
                request.TaskId);
            return Result.Fail(ErrorsFactory.BadRequest("Task",
                "Не указаны изменения для задачи. Необходимо изменить колонку или порядок."));
        }

        //TODO уменьшить вызовы к БД
        if (newColumnId is null && newKanbanOrder.HasValue)
        {
            _logger.LogInformation("Изменение порядка задачи {TaskId} в колонке {ColumnId} с {OldOrder} на {NewOrder}",
                request.TaskId, request.ColumnId, task.KanbanOrder ?? 0, request.NewOrder);
            await _taskOrderService.ShiftTaskOrdersAsync(request.ColumnId, request.NewOrder, task.KanbanOrder ?? 0,
                cancellationToken);
        }
        else if (newColumnId.HasValue)
        {
            _logger.LogInformation(
                "Перемещение задачи {TaskId} из колонки {OldColumnId} в колонку {NewColumnId} с порядком {NewOrder}",
                request.TaskId, task.KanbanColumnId, request.ColumnId, request.NewOrder);
            await _taskOrderService.ShiftTaskOrdersAsync(request.ColumnId, request.NewOrder, null, cancellationToken);

            if (task.KanbanColumnId.HasValue)
            {
                await _taskOrderService.CompactTaskOrdersAsync(task.KanbanColumnId.Value, task.KanbanOrder ?? 0,
                    cancellationToken);
            }
        }

        task.KanbanColumnId = newColumnId ?? task.KanbanColumnId;
        task.KanbanOrder = newKanbanOrder ?? task.KanbanOrder;

        // Synchronize task status with column status when moving to a different column
        if (newColumnId.HasValue && task.Status != column.Status)
        {
            _logger.LogInformation("Синхронизация статуса задачи {TaskId} с колонкой {ColumnId}: {OldStatus} -> {NewStatus}",
                task.Id, column.Id, task.Status, column.Status);
            _taskStatusTracker.TrackTimeAndStatusChange(task, column.Status);
        }

        await _tasksRepository.UpdateAsync(task, cancellationToken);

        _logger.LogInformation("Задача {TaskId} успешно перемещена в колонку {ColumnId} с порядком {Order}",
            request.TaskId, task.KanbanColumnId, task.KanbanOrder);

        return Result.Ok();
    }
}