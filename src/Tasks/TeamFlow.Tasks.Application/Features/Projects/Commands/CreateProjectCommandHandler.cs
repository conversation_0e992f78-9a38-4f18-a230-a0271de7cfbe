using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Projects.Commands;

public sealed class CreateProjectCommandHandler : IRequestHandler<CreateProjectCommand, Result<Guid>>
{
    private readonly IRepository<Project, Guid> _projectsRepository;
    private readonly IExternalUserService _externalUserService;
    private readonly IKanbanColumnInitializationService _kanbanColumnInitializationService;
    private readonly ILogger<CreateProjectCommandHandler> _logger;
    private readonly IValidator<CreateProjectCommand> _validator;

    public CreateProjectCommandHandler(IRepository<Project, Guid> projectsRepository, IRepository<Team, Guid> teamsRepository,
        IExternalUserService externalUserService, IKanbanColumnInitializationService kanbanColumnInitializationService,
        ILogger<CreateProjectCommandHandler> logger, IValidator<CreateProjectCommand> validator)
    {
        _projectsRepository = projectsRepository;
        _externalUserService = externalUserService;
        _kanbanColumnInitializationService = kanbanColumnInitializationService;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result<Guid>> Handle(CreateProjectCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Создание нового проекта с названием: {Name}", request.Name);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "создании проекта");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Project), validationResult));
        }

        var isExistingProject = await _projectsRepository.FindAnyAsync(x => x.Name == request.Name, cancellationToken);
        if (isExistingProject)
        {
            _logger.LogWarning("Проект с названием {Name} уже существует", request.Name);
            return Result.Fail(ErrorsFactory.AlreadyExists(nameof(Project), "Name", request.Name));
        }

        var owner = await _externalUserService.GetByGuidAsync(request.OwnerId, cancellationToken);

        if (owner.IsFailed)
        {
            _logger.LogWarning("Пользователь  с идентификатором '{OwnerId}' не найден", request.OwnerId);
            return Result.Fail(ErrorsFactory.NotFound("User", request.OwnerId));
        }

        var project = new Project
        {
            Id = Guid.CreateVersion7(),
            Name = request.Name,
            Description = request.Description,
            Status = request.Status,
            PriorityLevel = request.PriorityLevel,
            OwnerId = request.OwnerId,
            StartDate = request.StartDate,
            EndDate = request.EndDate
        };

        await _projectsRepository.AddAsync(project, cancellationToken);
        _logger.LogInformation("Проект с ID {ProjectId} успешно создан", project.Id);

        // Create default kanban columns for the project
        var columnsResult = await _kanbanColumnInitializationService.CreateDefaultColumnsForProjectAsync(project.Id, cancellationToken);
        if (columnsResult.IsFailed)
        {
            _logger.LogWarning("Не удалось создать колонки по умолчанию для проекта {ProjectId}: {Errors}",
                project.Id, string.Join(", ", columnsResult.Errors.Select(e => e.Message)));
            // Note: We don't fail the entire operation if column creation fails
            // The project is still created successfully
        }

        return Result.Ok(project.Id);
    }
}