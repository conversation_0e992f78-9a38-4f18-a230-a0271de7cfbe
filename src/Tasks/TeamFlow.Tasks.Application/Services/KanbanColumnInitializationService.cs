using FluentResults;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services;
using TeamFlow.Tasks.Core.Entities;
using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Application.Services;

public class KanbanColumnInitializationService : IKanbanColumnInitializationService
{
    private readonly IRepository<KanbanColumn, Guid> _columnsRepository;
    private readonly ILogger<KanbanColumnInitializationService> _logger;

    public KanbanColumnInitializationService(
        IRepository<KanbanColumn, Guid> columnsRepository,
        ILogger<KanbanColumnInitializationService> logger)
    {
        _columnsRepository = columnsRepository;
        _logger = logger;
    }

    public async Task<Result> CreateDefaultColumnsForProjectAsync(Guid projectId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Создание колонок по умолчанию для проекта {ProjectId}", projectId);

        try
        {
            var defaultColumns = new List<KanbanColumn>
            {
                new()
                {
                    Id = Guid.CreateVersion7(),
                    ProjectId = projectId,
                    Name = "Открыто",
                    Status = TaskStatuses.Open,
                    Order = 0
                },
                new()
                {
                    Id = Guid.CreateVersion7(),
                    ProjectId = projectId,
                    Name = "В работе",
                    Status = TaskStatuses.InProgress,
                    Order = 1
                },
                new()
                {
                    Id = Guid.CreateVersion7(),
                    ProjectId = projectId,
                    Name = "Заблокировано",
                    Status = TaskStatuses.Blocked,
                    Order = 2
                },
                new()
                {
                    Id = Guid.CreateVersion7(),
                    ProjectId = projectId,
                    Name = "Ожидание",
                    Status = TaskStatuses.Waiting,
                    Order = 3
                },
                new()
                {
                    Id = Guid.CreateVersion7(),
                    ProjectId = projectId,
                    Name = "Завершено",
                    Status = TaskStatuses.Completed,
                    Order = 4
                },
                new()
                {
                    Id = Guid.CreateVersion7(),
                    ProjectId = projectId,
                    Name = "Отменено",
                    Status = TaskStatuses.Cancelled,
                    Order = 5
                }
            };

            await _columnsRepository.BulkInsertAsync(defaultColumns, cancellationToken);
            
            _logger.LogInformation("Успешно создано {ColumnCount} колонок по умолчанию для проекта {ProjectId}", 
                defaultColumns.Count, projectId);

            return Result.Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ошибка при создании колонок по умолчанию для проекта {ProjectId}", projectId);
            return Result.Fail($"Ошибка при создании колонок по умолчанию: {ex.Message}");
        }
    }
}
