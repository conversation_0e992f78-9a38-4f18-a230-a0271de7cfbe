using System.Linq.Expressions;
using TeamFlow.Shared.Repositories.Entities;
using TeamFlow.Shared.Repositories.Repositories.Common;

namespace TeamFlow.Identity.Core.Repositories;

public interface IGenericRepository<TEntity, in TKey>
    : IGetOne<TEntity>, IGetMany<TEntity>, IAdd<TEntity>, IUpdate<TEntity>,
    IRemove<TEntity>, IFindAny<TEntity>, IBulkOperations<TEntity, TKey>, ITransactional
    where TEntity : class, IEntity<TKey> where TKey : IEquatable<TKey>
{
    /// <summary>
    /// Асинхронно получает одну сущность с включением связанных данных
    /// </summary>
    /// <param name="predicate">Предикат для поиска сущности</param>
    /// <param name="includes">Выражения для включения связанных данных</param>
    /// <param name="cancellationToken">Токен отмены</param>
    /// <returns>Сущность с загруженными связанными данными или null</returns>
    Task<TEntity?> GetOneWithIncludesAsync(
        Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default,
        params Expression<Func<TEntity, object>>[] includes);
}