using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels.Profile;
using TeamFlow.Identity.Application.Contracts.Features.Queries.User;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Mapping;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Queries.User;

public class GetCurrentUserProfileQueryHandler : IRequestHandler<GetCurrentUserProfileQuery, Result<CurrentUserProfileViewModel>>
{
    private readonly IUsersRepository _usersRepository;
    private readonly IValidator<GetCurrentUserProfileQuery> _validator;
    private readonly ILogger<GetCurrentUserProfileQueryHandler> _logger;

    public GetCurrentUserProfileQueryHandler(
        IUsersRepository usersRepository,
        IValidator<GetCurrentUserProfileQuery> validator,
        ILogger<GetCurrentUserProfileQueryHandler> logger)
    {
        _usersRepository = usersRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result<CurrentUserProfileViewModel>> Handle(GetCurrentUserProfileQuery request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Core.Entities.User), validationResult));
        }

        try
        {
            var user = await _usersRepository.GetUserWithProfileDataAsync(request.UserId, cancellationToken);

            if (user is null)
            {
                return Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.User), request.UserId));
            }

            var profileViewModel = user.ToCurrentUserProfileViewModel();
            return Result.Ok(profileViewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving current user profile for user {UserId}", request.UserId);
            return Result.Fail(ErrorsFactory.InternalServerError("Ошибка при получении профиля пользователя"));
        }
    }
}
