using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Dto.ViewModels.Profile;
using TeamFlow.Identity.Application.Contracts.Features.Queries.User;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Mapping;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Queries.User;

public class GetPublicUserProfileQueryHandler : IRequestHandler<GetPublicUserProfileQuery, Result<PublicUserProfileViewModel>>
{
    private readonly IUsersRepository _usersRepository;
    private readonly IValidator<GetPublicUserProfileQuery> _validator;
    private readonly ILogger<GetPublicUserProfileQueryHandler> _logger;

    public GetPublicUserProfileQueryHandler(
        IUsersRepository usersRepository,
        IValidator<GetPublicUserProfileQuery> validator,
        ILogger<GetPublicUserProfileQueryHandler> logger)
    {
        _usersRepository = usersRepository;
        _validator = validator;
        _logger = logger;
    }

    public async Task<Result<PublicUserProfileViewModel>> Handle(GetPublicUserProfileQuery request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Core.Entities.User), validationResult));
        }

        try
        {
            var user = await _usersRepository.GetUserWithPublicProfileDataAsync(request.UserId, cancellationToken);

            if (user is null)
            {
                return Result.Fail(ErrorsFactory.NotFound(nameof(Core.Entities.User), request.UserId));
            }

            var publicProfileViewModel = user.ToPublicUserProfileViewModel();
            return Result.Ok(publicProfileViewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving public user profile for user {UserId}", request.UserId);
            return Result.Fail(ErrorsFactory.InternalServerError("Ошибка при получении публичного профиля пользователя"));
        }
    }
}
